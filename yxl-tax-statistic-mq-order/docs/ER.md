# 数据库ER图文档

## 概述

本文档描述了税务统计MQ订单同步系统的数据库表结构及其关系。系统主要涉及订单管理、商品管理、店铺管理、发票管理、退货管理等核心业务模块。

## 核心实体关系图

```mermaid
erDiagram
    %% 系统管理模块
    SYS_COMPANY {
        bigint id PK
        bigint parent_company_id FK
        int company_type
        varchar company_name
        varchar license_number
        bigint dept_id
        varchar address
        varchar contact_name
        varchar contact_mobile
        int enable_status
        varchar invitation_code
        varchar bank_name
        varchar bank_card
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    SYS_MARKET {
        bigint id PK
        varchar market_name
        bigint province_id
        bigint city_id
        bigint district_id
        varchar city_info
        varchar address
        int management_model
        varchar legal_person
        varchar person_mobile
        varchar introduction
        varchar remark
        int enable_status
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    SYS_COMPANY_CONFIG {
        bigint id PK
        bigint company_id FK
        int disassemble_status
        decimal disassemble_min_money
        decimal disassemble_max_money
        decimal disassemble_start_money
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    SYS_COMPANY_PAY_TYPE {
        bigint id PK
        bigint company_id FK
        varchar mch_id
        varchar mch_key
        varchar other_set_wo
        varchar other_set
        int pay_type
        int valid_type
        int default_type
        int app_valid_type
        int app_default_type
        int enable_status
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
        int del_flag
    }
    
    SYS_MIGRATE {
        bigint id PK
        bigint in_company_id FK
        bigint out_company_id FK
        bigint shop_unique FK
        int audit_status
        varchar audit_content
        bigint audit_user
        datetime audit_time
        varchar remark
        int enable_status
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
        int del_flag
    }
    
    %% 商户管理模块
    BUS_SHOP {
        bigint id PK
        bigint shop_unique UK
        varchar shop_name
        int shop_type
        varchar address
        varchar shop_phone
        varchar county_code
        varchar town_code
        int enable_status
        bigint company_id FK
        varchar sub_acc_no
        decimal cooperator_cost_proportion
        varchar supplier_no
        varchar invitation_code
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    BUS_SHOP_BILL {
        bigint id PK
        bigint company_id FK
        bigint shop_unique FK
        decimal settled_amount
        decimal unsettled_amount
        decimal settleding_amount
        varchar settled_type
        varchar remark
        int del_flag
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    %% 商品管理模块
    BUS_GOODS {
        bigint id PK
        bigint goods_id
        bigint shop_unique FK
        varchar goods_barcode
        varchar goods_name
        varchar goods_alias
        decimal goods_in_price
        decimal goods_sale_price
        decimal goods_web_sale_price
        int goods_life
        varchar goods_standard
        varchar goods_unit
        bigint category_id FK
        bigint category_two_id FK
        bigint company_id FK
        varchar remark
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
        int del_flag
    }
    
    BUS_GOODS_CATEGORY {
        bigint id PK
        bigint parent_id FK
        varchar ancestors
        varchar category_name
        int enable_status
        bigint company_id FK
        varchar remark
        int category_type
        varchar category_no
        varchar goods_name
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
        int del_flag
    }
    
    %% 客户管理模块
    BUS_CUSTOMER {
        bigint id PK
        varchar cus_unique UK
        varchar pc_nick_name
        int pc_sex
        varchar pc_birthday
        int enable_status
        int del_flag
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    %% 订单管理模块
    SALE_LIST {
        bigint id PK
        bigint company_id FK
        bigint shop_unique FK
        varchar shop_name
        int sale_type
        varchar sale_list_unique UK
        varchar cus_unique FK
        varchar sale_list_name
        decimal sale_list_total
        decimal sale_list_actually_received
        decimal sale_list_service_fee
        varchar trade_no
        datetime pay_time
        decimal pay_fee
        datetime sale_list_datetime
        int sale_list_state
        int sale_list_payment
        int settled_status
        int profit_status
        decimal profit_total
        varchar contract_no
        varchar order_type
        varchar parent_list_unique
        datetime create_time
        datetime modify_time
    }
    
    SALE_LIST_DETAIL {
        bigint id PK
        bigint company_id FK
        bigint sale_list_detail_id
        varchar sale_list_unique FK
        bigint goods_id FK
        varchar goods_barcode
        varchar goods_name
        decimal sale_list_detail_count
        decimal sale_list_detail_price
        decimal sale_list_detail_subtotal
        datetime create_time
        datetime modify_time
    }
    
    SALE_LIST_PAY_DETAIL {
        bigint id PK
        bigint company_id FK
        varchar sale_list_unique FK
        int pay_method
        decimal pay_money
        int server_type
        varchar mch_id
        datetime pay_time
        datetime create_time
        datetime modify_time
    }
    
    %% 拆解订单模块
    SALE_LIST_DISASSEMBLE {
        bigint id PK
        bigint company_id FK
        bigint shop_unique FK
        varchar shop_name
        int sale_type
        varchar sale_list_unique UK
        varchar cus_unique FK
        varchar sale_list_name
        decimal sale_list_total
        decimal sale_list_actually_received
        decimal sale_list_service_fee
        varchar trade_no
        datetime pay_time
        decimal pay_fee
        datetime sale_list_datetime
        int sale_list_state
        int sale_list_payment
        int settled_status
        int profit_status
        decimal profit_total
        varchar contract_no
        varchar order_type
        int parent_list_unique
        datetime create_time
        datetime modify_time
    }
    
    SALE_LIST_DISASSEMBLE_DETAIL {
        bigint id PK
        bigint company_id FK
        bigint sale_list_detail_id
        varchar sale_list_unique FK
        bigint goods_id FK
        varchar goods_barcode
        varchar goods_name
        decimal sale_list_detail_count
        decimal sale_list_detail_price
        decimal sale_list_detail_subtotal
        datetime create_time
        datetime modify_time
    }
    
    SALE_LIST_DISASSEMBLE_PAY_DETAIL {
        bigint id PK
        bigint company_id FK
        varchar sale_list_unique FK
        int pay_method
        decimal pay_money
        int server_type
        varchar mch_id
        datetime pay_time
        datetime create_time
        datetime modify_time
    }
    
    DISASSEMBLE_LIST {
        bigint id PK
        bigint company_id FK
        varchar sale_list_unique FK
        int disassemble_count
        int disassemble_status
        decimal min_amount_set
        decimal max_amount_set
        decimal min_amount_actual
        decimal max_amount_actual
        varchar disassmeble_remarks
        decimal order_amount
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    %% 退货管理模块
    RETURN_LIST {
        bigint id PK
        bigint company_id FK
        varchar sale_list_unique FK
        bigint shop_unique FK
        varchar shop_name
        datetime ret_list_datetime
        varchar ret_list_state
        varchar ret_list_handlestate
        int ret_money_type
        decimal ret_list_total_money
        datetime ret_back_datetime
        varchar ret_list_unique UK
        varchar ret_list_reason
        decimal ret_list_delfee
        decimal return_sale_list_service_fee
        datetime create_time
        datetime modify_time
    }
    
    RETURN_LIST_DETAIL {
        bigint id PK
        bigint company_id FK
        bigint ret_list_detail_id
        varchar sale_list_unique FK
        varchar goods_barcode
        bigint goods_id FK
        varchar goods_name
        decimal ret_list_detail_count
        decimal ret_list_detail_price
        int handle_way
        decimal ret_list_origin_price
        varchar ret_list_unique FK
        bigint rsale_list_detail_id
        datetime create_time
        datetime modify_time
    }
    
    RETURN_LIST_PAYDETAIL {
        bigint id PK
        bigint company_id FK
        varchar sale_list_unique FK
        varchar ret_list_unique FK
        int pay_type
        decimal pay_money
        int service_type
        varchar mch_id
        datetime create_time
        datetime modify_time
    }
    
    %% 发票管理模块
    BUS_SHOP_INVOICE {
        bigint id PK
        bigint company_id FK
        bigint shop_unique FK
        varchar sale_list_unique FK
        int invoice_type
        varchar invoice_code
        varchar machine_code
        varchar invoice_number
        int medium_type
        int invoice_kind
        datetime invoice_date
        varchar check_code
        int purchase_type
        varchar purchase_name
        varchar purchase_identity
        varchar purchase_address
        varchar purchase_phone
        varchar purchase_bank
        varchar purchase_bank_no
        varchar sale_name
        varchar sale_identity
        varchar sale_address
        varchar sale_phone
        varchar sale_bank
        varchar sale_bank_no
        decimal order_money
        decimal tax_money
        decimal order_tax_money
        varchar payee
        varchar checker
        varchar invoice_man
        int status
        varchar image_url
        varchar bill_number
        int periods_level
        varchar receive_msg
        varchar notes
        int purchase_person_flag
        int sale_person_flag
        int purchase_bank_flag
        int sale_bank_flag
        int apply_flag
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    BUS_SHOP_INVOICE_DETAIL {
        bigint id PK
        bigint invoice_id FK
        varchar tax_classification_code
        varchar goods_name
        varchar goods_sm
        varchar spec
        varchar unit
        decimal price
        decimal quantity
        decimal amount
        decimal tax_rate
        decimal tax_amount
        varchar tax_classification_name
        datetime create_time
        datetime modify_time
    }
    
    BUS_SHOP_INVOICE_SETTING {
        bigint id PK
        bigint company_id FK
        varchar company_name
        varchar company_tax_no
        varchar company_address
        varchar company_phone
        varchar invoice_man
        varchar checker
        varchar payee
        varchar invoice_bank_name
        varchar invoice_bank_card
        int periods_level
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    %% 异常记录模块
    BUS_MQ_ERROR_RECORD {
        bigint id PK
        varchar shop_unique
        varchar sale_list_unique
        varchar remark
        int del_flag
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    %% 统计临时表
    STBUS_SALE_LIST_TMP {
        bigint id PK
        bigint province_id
        varchar province_name
        bigint city_id
        varchar city_name
        bigint district_id
        varchar district_name
        bigint market_id
        varchar market_name
        bigint company_id FK
        varchar company_name
        bigint shop_unique FK
        varchar shop_name
        varchar sale_list_unique
        datetime sale_list_datetime
        varchar cus_unique
        varchar customer_name
        varchar goods_barcode
        varchar goods_name
        decimal goods_count
        decimal sale_amount
        varchar pay_method
        datetime create_time
        datetime modify_time
        bigint create_user
        bigint modify_user
    }
    
    %% 地区信息表
    CNAREA_2023 {
        bigint id PK
        int level
        bigint parent_code
        bigint area_code
        bigint short_parent_code
        bigint short_area_code
        varchar zip_code
        varchar city_code
        varchar name
        varchar short_name
        varchar merger_name
        varchar pinyin
        decimal lng
        decimal lat
    }
    
    %% 关系定义
    SYS_COMPANY ||--o{ SYS_COMPANY : "parent_company_id"
    SYS_COMPANY ||--o{ SYS_COMPANY_CONFIG : "company_id"
    SYS_COMPANY ||--o{ SYS_COMPANY_PAY_TYPE : "company_id"
    SYS_COMPANY ||--o{ BUS_SHOP : "company_id"
    SYS_COMPANY ||--o{ BUS_GOODS : "company_id"
    SYS_COMPANY ||--o{ BUS_GOODS_CATEGORY : "company_id"
    SYS_COMPANY ||--o{ SALE_LIST : "company_id"
    SYS_COMPANY ||--o{ BUS_SHOP_INVOICE_SETTING : "company_id"
    
    BUS_SHOP ||--o{ SALE_LIST : "shop_unique"
    BUS_SHOP ||--o{ BUS_SHOP_BILL : "shop_unique"
    BUS_SHOP ||--o{ BUS_GOODS : "shop_unique"
    BUS_SHOP ||--o{ BUS_SHOP_INVOICE : "shop_unique"
    BUS_SHOP ||--o{ RETURN_LIST : "shop_unique"
    BUS_SHOP ||--o{ SALE_LIST_DISASSEMBLE : "shop_unique"
    BUS_SHOP ||--o{ STBUS_SALE_LIST_TMP : "shop_unique"
    
    BUS_CUSTOMER ||--o{ SALE_LIST : "cus_unique"
    BUS_CUSTOMER ||--o{ SALE_LIST_DISASSEMBLE : "cus_unique"
    
    BUS_GOODS_CATEGORY ||--o{ BUS_GOODS_CATEGORY : "parent_id"
    BUS_GOODS_CATEGORY ||--o{ BUS_GOODS : "category_id"
    BUS_GOODS_CATEGORY ||--o{ BUS_GOODS : "category_two_id"
    
    BUS_GOODS ||--o{ SALE_LIST_DETAIL : "goods_id"
    BUS_GOODS ||--o{ SALE_LIST_DISASSEMBLE_DETAIL : "goods_id"
    BUS_GOODS ||--o{ RETURN_LIST_DETAIL : "goods_id"
    
    SALE_LIST ||--o{ SALE_LIST_DETAIL : "sale_list_unique"
    SALE_LIST ||--o{ SALE_LIST_PAY_DETAIL : "sale_list_unique"
    SALE_LIST ||--o{ BUS_SHOP_INVOICE : "sale_list_unique"
    SALE_LIST ||--o{ RETURN_LIST : "sale_list_unique"
    SALE_LIST ||--o{ DISASSEMBLE_LIST : "sale_list_unique"
    
    SALE_LIST_DISASSEMBLE ||--o{ SALE_LIST_DISASSEMBLE_DETAIL : "sale_list_unique"
    SALE_LIST_DISASSEMBLE ||--o{ SALE_LIST_DISASSEMBLE_PAY_DETAIL : "sale_list_unique"
    
    RETURN_LIST ||--o{ RETURN_LIST_DETAIL : "ret_list_unique"
    RETURN_LIST ||--o{ RETURN_LIST_PAYDETAIL : "ret_list_unique"
    
    BUS_SHOP_INVOICE ||--o{ BUS_SHOP_INVOICE_DETAIL : "invoice_id"
    
    SYS_MIGRATE }o--|| SYS_COMPANY : "in_company_id"
    SYS_MIGRATE }o--|| SYS_COMPANY : "out_company_id"
    SYS_MIGRATE }o--|| BUS_SHOP : "shop_unique"
```

## 表结构详细说明

### 1. 系统管理模块

#### 1.1 公司信息表 (sys_company)
- **用途**: 存储企业/公司基本信息
- **关键字段**: 
  - `parent_company_id`: 支持公司层级结构
  - `company_type`: 1-系统平台, 2-总公司, 3-分公司
  - `license_number`: 统一社会信用代码

#### 1.2 市场管理表 (sys_market)
- **用途**: 管理各个市场的基本信息
- **关键字段**: 
  - `management_model`: 0-企业独营, 1-企业联营

#### 1.3 公司配置表 (sys_company_config)
- **用途**: 存储公司级别的业务配置
- **关键字段**: 
  - `disassemble_status`: 是否开启订单拆解功能
  - 拆解相关金额配置

#### 1.4 公司支付配置表 (sys_company_pay_type)
- **用途**: 管理公司的支付方式配置
- **关键字段**: 
  - `pay_type`: 支付渠道类型
  - 线上线下支付方式配置

#### 1.5 迁移管理表 (sys_migrate)
- **用途**: 管理店铺在企业间的迁入迁出
- **关键字段**: 
  - `audit_status`: 0-待审核, 1-通过, 2-不通过

### 2. 商户管理模块

#### 2.1 商户信息表 (bus_shop)
- **用途**: 存储商户/店铺基本信息
- **关键字段**: 
  - `shop_unique`: 商户唯一标识
  - `shop_type`: 店铺类型(便利店、水果店等)
  - `company_id`: 所属企业

#### 2.2 商户账单表 (bus_shop_bill)
- **用途**: 管理商户的结算账单信息
- **关键字段**: 
  - 已结算、未结算、结算中金额
  - `settled_type`: 结算方式

### 3. 商品管理模块

#### 3.1 商品信息表 (bus_goods)
- **用途**: 存储商品基本信息
- **关键字段**: 
  - `goods_barcode`: 商品条形码
  - `category_id`, `category_two_id`: 一级、二级分类

#### 3.2 商品分类表 (bus_goods_category)
- **用途**: 管理商品分类层级结构
- **关键字段**: 
  - `parent_id`: 支持分类层级
  - `category_type`: 1-默认分类, 2-普通分类

### 4. 客户管理模块

#### 4.1 客户信息表 (bus_customer)
- **用途**: 存储客户基本信息
- **关键字段**: 
  - `cus_unique`: 客户唯一标识

### 5. 订单管理模块

#### 5.1 销售订单表 (sale_list)
- **用途**: 存储销售订单主信息
- **关键字段**: 
  - `sale_list_unique`: 订单唯一编号
  - `sale_type`: 订单类型(实体店、APP、小程序等)
  - `sale_list_state`: 付款状态
  - `sale_list_payment`: 支付方式

#### 5.2 销售订单明细表 (sale_list_detail)
- **用途**: 存储订单商品明细
- **关键字段**: 
  - 商品信息、数量、价格、小计

#### 5.3 销售订单支付明细表 (sale_list_pay_detail)
- **用途**: 存储订单支付明细(支持混合支付)
- **关键字段**: 
  - `pay_method`: 支付方式
  - `server_type`: 支付服务商

### 6. 订单拆解模块

#### 6.1 拆解订单表 (sale_list_disassemble)
- **用途**: 存储拆解后的订单信息
- **结构**: 与原订单表结构相同

#### 6.2 拆解记录表 (disassemble_list)
- **用途**: 记录订单拆解的配置和状态
- **关键字段**: 
  - `disassemble_count`: 拆解订单数量
  - 拆解金额配置

### 7. 退货管理模块

#### 7.1 退货单表 (return_list)
- **用途**: 管理退货申请信息
- **关键字段**: 
  - `ret_list_state`: 退款状态
  - `ret_list_handlestate`: 受理状态

#### 7.2 退货明细表 (return_list_detail)
- **用途**: 退货商品明细
- **关键字段**: 
  - `handle_way`: 退货处理方式

#### 7.3 退货支付明细表 (return_list_paydetail)
- **用途**: 退款支付明细

### 8. 发票管理模块

#### 8.1 发票主表 (bus_shop_invoice)
- **用途**: 管理发票开具信息
- **关键字段**: 
  - `invoice_type`: 1-进项票, 2-销项票
  - `invoice_kind`: 1-普通发票, 2-专用发票
  - `status`: 开票状态

#### 8.2 发票明细表 (bus_shop_invoice_detail)
- **用途**: 发票商品明细
- **关键字段**: 
  - 税收分类编码、税率、税额等

#### 8.3 发票设置表 (bus_shop_invoice_setting)
- **用途**: 企业开票配置信息

### 9. 辅助模块

#### 9.1 异常记录表 (bus_mq_error_record)
- **用途**: 记录MQ同步异常信息

#### 9.2 统计临时表 (stbus_sale_list_tmp)
- **用途**: 最近两天订单统计数据

#### 9.3 地区信息表 (cnarea_2023)
- **用途**: 中国行政地区信息

## 核心业务流程

### 订单处理流程
1. 创建销售订单 (`sale_list`)
2. 添加订单明细 (`sale_list_detail`)
3. 记录支付信息 (`sale_list_pay_detail`)
4. 可选：订单拆解 (`disassemble_list` → `sale_list_disassemble`)
5. 可选：开具发票 (`bus_shop_invoice`)
6. 可选：退货处理 (`return_list`)

### 数据同步流程
- MQ消息队列同步订单数据
- 异常情况记录到 `bus_mq_error_record`
- 统计数据更新到 `stbus_sale_list_tmp`

## 索引建议

### 主要索引
- 订单表：`sale_list_unique`, `shop_unique`, `company_id`, `sale_list_datetime`
- 商品表：`goods_barcode`, `shop_unique`, `category_id`
- 客户表：`cus_unique`
- 发票表：`invoice_number`, `shop_unique`, `sale_list_unique`

### 复合索引
- `(company_id, shop_unique, sale_list_datetime)` - 用于企业订单查询
- `(shop_unique, sale_list_state, sale_list_datetime)` - 用于店铺订单状态查询

## 数据完整性约束

### 外键约束
- 所有 `company_id` 必须存在于 `sys_company` 表
- 所有 `shop_unique` 必须存在于 `bus_shop` 表
- 订单明细的 `sale_list_unique` 必须存在于对应订单表

### 业务约束
- 订单金额字段不能为负数
- 订单状态必须在预定义范围内
- 支付方式必须与企业配置的支付类型匹配

## 性能优化建议

### 分区策略
- 订单相关表按月分区 (`sale_list_datetime`)
- 统计表按日分区

### 归档策略
- 历史订单数据定期归档
- 异常记录表定期清理

## 代码链接

### 实体类文件
- [公司信息实体](../src/main/java/cc/buyhoo/tax/mq/entity/SysCompanyEntity.java)
- [商户信息实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusShopEntity.java)
- [商品信息实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusGoodsEntity.java)
- [客户信息实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusCustomerEntity.java)
- [销售订单实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusSaleListEntity.java)
- [订单明细实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusSaleListDetailEntity.java)
- [支付明细实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusSaleListPayDetailEntity.java)
- [发票主表实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusShopInvoiceEntity.java)
- [发票明细实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusShopInvoiceDetailEntity.java)
- [退货单实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusReturnListEntity.java)
- [退货明细实体](../src/main/java/cc/buyhoo/tax/mq/entity/BusReturnListDetailEntity.java)

### Mapper文件
- [客户信息Mapper](../src/main/resources/mapper/BusCustomerMapper.xml)
- [地区信息Mapper](../src/main/resources/mapper/Cnarea2023Mapper.xml)
- [公司配置Mapper](../src/main/resources/mapper/SysCompanyConfigMapper.xml)
- [市场管理Mapper](../src/main/resources/mapper/SysMarketMapper.xml)
- [迁移管理Mapper](../src/main/resources/mapper/SysMigrateMapper.xml)
- [统计临时表Mapper](../src/main/resources/mapper/StbusSaleListTmpMapper.xml)

## 数据字典

### 枚举值说明

#### 公司类型 (company_type)
- 1: 系统平台
- 2: 总公司
- 3: 分公司

#### 店铺类型 (shop_type)
- 0: 其他
- 1: 便利店
- 2: 水果店
- 3: 母婴店
- 4: 益农中心站
- 5: 益农标准站、简易站、专业站
- 6: 宁宇总店
- 7: 宁宇分店
- 8: 五金店
- 9: 学校
- 10: 机关食堂
- 11: 加油站
- 12: 餐饮店
- 13: 拼团店

#### 订单类型 (sale_type)
- 0: 实体店销售
- 1: APP订单
- 2: 微信商城小程序
- 3: 网页订单
- 4: 美团外卖订单
- 5: 饿了么外卖订单
- 6: 移动端收银
- 7: 收银端平台会员结算
- 8: 积分兑换
- 21: 堂食订单(餐饮)
- 22: 打包订单(餐饮)
- 23: 外卖订单(餐饮)

#### 付款状态 (sale_list_state)
- 1: 货到付款未付款
- 2: 网上订单未付款
- 3: 已付款
- 4: 赊账
- 5: 申请退款
- 6: 同意退款
- 7: 拒绝退款
- 8: 自助收银未付款
- 21: 未付款(餐饮)
- 22: 已付款(餐饮)
- 23: 支付中(餐饮)

#### 支付方式 (sale_list_payment)
- 1: 现金
- 2: 支付宝
- 3: 微信
- 4: 银行卡
- 5: 储值卡
- 6: 美团外卖
- 7: 饿了么外卖
- 8: 混合支付
- 9: 免密支付
- 10: 积分兑换
- 11: 百货豆
- 12: 拉卡拉
- 13: 易通付款码支付
- 14: 金圈聚合码
- 31-44: 餐饮支付方式对应

#### 发票类型 (invoice_type)
- 1: 进项票
- 2: 销项票

#### 发票种类 (invoice_kind)
- 1: 普通发票
- 2: 专用发票

#### 发票介质 (medium_type)
- 1: 电子发票
- 2: 纸质发票

#### 开票状态 (status)
- 1: 已开票
- 2: 未开票
- 3: 开票中
- 4: 开票失败

## 注意事项

### 数据一致性
1. **订单拆解**: 拆解后的订单存储在独立的表中，需要维护与原订单的关联关系
2. **混合支付**: 一个订单可能有多种支付方式，通过支付明细表记录
3. **退货处理**: 退货可能是部分退货，需要记录具体的退货商品和数量
4. **发票管理**: 一个订单可能对应多张发票，支持分批开票

### 性能考虑
1. **大数据量**: 订单表数据量较大，建议按时间分区
2. **查询优化**: 常用查询字段建立合适的索引
3. **统计报表**: 使用临时统计表提高查询性能

### 扩展性
1. **多租户**: 通过company_id实现多企业数据隔离
2. **业务扩展**: 预留了备注字段和扩展字段
3. **状态管理**: 使用枚举值管理各种状态，便于扩展

---

*本文档基于当前系统实体类分析生成，版本：v1.0，最后更新：2024-12-19*
*如有数据库结构变更，请及时更新此文档并同步代码链接。*
